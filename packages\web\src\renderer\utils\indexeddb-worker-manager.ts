/**
 * IndexedDB Worker 管理器
 * 负责创建、管理和与 IndexedDB Worker 通信
 */

import { 
  WORKER_MESSAGE_TYPES, 
  type WorkerCallbacks, 
  type WorkerMessage,
  type ProgressUpdateData,
  type ErrorData,
  type IndexedDBResult
} from '@src/types/apidoc/worker'

export class IndexedDBWorkerManager {
  private worker: Worker | null = null
  private isReady = false
  private callbacks: WorkerCallbacks = {}

  /**
   * 初始化 Worker
   */
  async init(): Promise<void> {
    if (this.worker) {
      return // 已经初始化
    }

    return new Promise((resolve, reject) => {
      try {
        // 创建 Worker 实例
        this.worker = new Worker(
          new URL('../workers/indexeddb.js', import.meta.url),
          { type: 'module' }
        )

        // 监听 Worker 消息
        this.worker.onmessage = (event: MessageEvent<WorkerMessage>) => {
          this.handleWorkerMessage(event.data)
        }

        // 监听 Worker 错误
        this.worker.onerror = (error) => {
          console.error('IndexedDB Worker 错误:', error)
          reject(new Error(`Worker 初始化失败: ${error.message}`))
        }

        // 等待 Worker 准备就绪
        const readyTimeout = setTimeout(() => {
          reject(new Error('Worker 初始化超时'))
        }, 10000) // 10秒超时

        this.callbacks.onReady = () => {
          clearTimeout(readyTimeout)
          this.isReady = true
          resolve()
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 处理 Worker 消息
   */
  private handleWorkerMessage(message: WorkerMessage): void {
    const { type, data } = message

    switch (type) {
      case WORKER_MESSAGE_TYPES.WORKER_READY:
        this.callbacks.onReady?.()
        break

      case WORKER_MESSAGE_TYPES.PROGRESS_UPDATE:
        this.callbacks.onProgress?.(data as ProgressUpdateData)
        break

      case WORKER_MESSAGE_TYPES.ERROR:
        this.callbacks.onError?.(data as ErrorData)
        break

      case WORKER_MESSAGE_TYPES.DATA_RESULT:
        this.callbacks.onResult?.(data as IndexedDBResult)
        break

      default:
        console.warn('未知的 Worker 消息类型:', type)
    }
  }

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks: WorkerCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks }
  }

  /**
   * 获取 IndexedDB 数据
   */
  async getIndexedDBData(): Promise<void> {
    if (!this.worker || !this.isReady) {
      throw new Error('Worker 未初始化或未准备就绪')
    }

    // 发送获取数据的消息
    this.worker.postMessage({
      type: WORKER_MESSAGE_TYPES.GET_INDEXEDDB_DATA,
      data: {}
    })
  }

  /**
   * 销毁 Worker
   */
  destroy(): void {
    if (this.worker) {
      this.worker.terminate()
      this.worker = null
      this.isReady = false
      this.callbacks = {}
    }
  }

  /**
   * 检查 Worker 是否准备就绪
   */
  get ready(): boolean {
    return this.isReady
  }
}

// 创建单例实例
export const indexedDBWorkerManager = new IndexedDBWorkerManager()
