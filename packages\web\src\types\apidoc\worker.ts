/**
 * Web Worker 相关类型定义
 */

// Worker 消息类型
export const WORKER_MESSAGE_TYPES = {
  GET_INDEXEDDB_DATA: 'GET_INDEXEDDB_DATA',
  PROGRESS_UPDATE: 'PROGRESS_UPDATE',
  DATA_RESULT: 'DATA_RESULT',
  ERROR: 'ERROR',
  WORKER_READY: 'WORKER_READY'
} as const

export type WorkerMessageType = typeof WORKER_MESSAGE_TYPES[keyof typeof WORKER_MESSAGE_TYPES]

// 进度更新消息数据
export interface ProgressUpdateData {
  progress: number // 0-100
  status: string
}

// 错误消息数据
export interface ErrorData {
  message: string
  stack?: string
}

// IndexedDB 数据结果
export interface IndexedDBResult {
  totalSize: number
  details: Array<{
    name: string
    size: number
    description: string
  }>
}

// Worker 消息结构
export interface WorkerMessage<T = any> {
  type: WorkerMessageType
  data: T
}

// 具体的消息类型
export interface ProgressMessage extends WorkerMessage<ProgressUpdateData> {
  type: typeof WORKER_MESSAGE_TYPES.PROGRESS_UPDATE
}

export interface ErrorMessage extends WorkerMessage<ErrorData> {
  type: typeof WORKER_MESSAGE_TYPES.ERROR
}

export interface DataResultMessage extends WorkerMessage<IndexedDBResult> {
  type: typeof WORKER_MESSAGE_TYPES.DATA_RESULT
}

export interface WorkerReadyMessage extends WorkerMessage<{ message: string }> {
  type: typeof WORKER_MESSAGE_TYPES.WORKER_READY
}

// Worker 事件回调类型
export interface WorkerCallbacks {
  onProgress?: (data: ProgressUpdateData) => void
  onError?: (data: ErrorData) => void
  onResult?: (data: IndexedDBResult) => void
  onReady?: () => void
}
